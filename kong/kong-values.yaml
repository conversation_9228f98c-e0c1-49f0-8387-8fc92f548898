controller:
  ingressController:
    image:
      tag: "3.4"
    env:
      feature_gates: "FillIDs=true"
    konnect:
      license:
        enabled: true
      enabled: true
      controlPlaneID: "83dc8b48-2214-4925-863c-f70775ff5110"
      tlsClientCertSecretName: konnect-client-tls
      apiHostname: "us.kic.api.konghq.com"

gateway:
  image:
    repository: kong/kong-gateway
    tag: "3.11"
  env:
    konnect_mode: "on"
    vitals: "off"
    cluster_mtls: pki
    cluster_telemetry_endpoint: "67302df654.us.tp0.konghq.com:443"
    cluster_telemetry_server_name: "67302df654.us.tp0.konghq.com"
    cluster_cert: /etc/secrets/konnect-client-tls/tls.crt
    cluster_cert_key: /etc/secrets/konnect-client-tls/tls.key
    lua_ssl_trusted_certificate: system
    nginx_worker_processes: "1"
    upstream_keepalive_max_requests: "100000"
    nginx_http_keepalive_requests: "100000"
    proxy_access_log: "off"
    dns_stale_ttl: "3600"
  resources:
    requests:
      cpu: 1
      memory: "2Gi"
  secretVolumes:
    - konnect-client-tls
  manager:
    enabled: false
