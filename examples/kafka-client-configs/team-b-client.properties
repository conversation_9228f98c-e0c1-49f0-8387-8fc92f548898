# Kafka Client Configuration for Team B
# Use this configuration to connect to KNEP as team-b

# Bootstrap servers (through Kong Gateway with TLS)
bootstrap.servers=bootstrap.team-b.127-0-0-1.sslip.io:9443

# Security configuration
security.protocol=SSL

# SSL configuration (for self-signed certificates)
ssl.truststore.location=/path/to/truststore.jks
ssl.truststore.password=changeit

# Alternative: Skip certificate verification (DEVELOPMENT ONLY)
# ssl.endpoint.identification.algorithm=
# ssl.check.hostname=false

# Producer specific settings
acks=all
retries=3
batch.size=16384
linger.ms=1
buffer.memory=33554432

# Consumer specific settings
group.id=team-b-consumer-group
auto.offset.reset=earliest
enable.auto.commit=true
auto.commit.interval.ms=1000
