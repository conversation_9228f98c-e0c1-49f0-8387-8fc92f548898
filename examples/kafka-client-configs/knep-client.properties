# Kafka Client Configuration for KNEP Client (mTLS)
# Use this configuration to test direct connection to Kafka using the knep-client certificate

# Bootstrap servers (direct to Kafka mTLS port)
bootstrap.servers=my-cluster-kafka-bootstrap.kafka.svc.cluster.local:9094

# Security configuration for mTLS
security.protocol=SSL

# SSL configuration (for self-signed certificates)
ssl.truststore.location=/path/to/truststore.jks
ssl.truststore.password=changeit
ssl.keystore.location=/path/to/knep-client-keystore.jks
ssl.keystore.password=changeit
ssl.key.password=changeit

# Alternative: Skip certificate verification (DEVELOPMENT ONLY)
# Uncomment these lines if you want to skip certificate validation
# ssl.endpoint.identification.algorithm=
# ssl.check.hostname=false

# Producer specific settings
acks=all
retries=3
batch.size=16384
linger.ms=1
buffer.memory=33554432

# Consumer specific settings
group.id=knep-client-test-group
auto.offset.reset=earliest
enable.auto.commit=true
auto.commit.interval.ms=1000

# Client ID for identification
client.id=knep-client-test

# Additional SSL settings for debugging (uncomment if needed)
# ssl.enabled.protocols=TLSv1.2,TLSv1.3
# ssl.protocol=TLS
