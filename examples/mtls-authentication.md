# mTLS Authentication with Kafka

This guide explains how to use mutual TLS (mTLS) authentication with the Kafka cluster.

## Overview

The Kafka cluster is configured with three listeners:
- **Plain** (port 9092): No encryption, no authentication
- **TLS** (port 9093): TLS encryption, no authentication
- **mTLS** (port 9094): TLS encryption with mutual TLS authentication

## KafkaUser Resources

The following KafkaUser resources are defined in `kafka/kafka-users.yaml` and created automatically when you deploy the Kafka cluster:

### 1. knep-client
- **Purpose**: For KNEP proxy to connect to Kafka
- **Permissions**: Full access to all topics and operations
- **Usage**: KNEP uses this to proxy requests from clients

### 2. team-a-user
- **Purpose**: Direct client access for team-a
- **Permissions**: Access to topics with `a-` prefix
- **Consumer Groups**: `team-a-*` pattern

### 3. team-b-user
- **Purpose**: Direct client access for team-b
- **Permissions**: Access to topics with `b-` prefix
- **Consumer Groups**: `team-b-*` pattern

## Extracting Client Certificates

### Option A: Automated Setup (Recommended)

Use the provided script to automatically extract certificates and create keystores:

```bash
./scripts/setup-knep-client-certs.sh
```

This script will:
- Extract all necessary certificates
- Create Java keystores and truststores
- Generate a ready-to-use properties file
- Provide testing instructions

### Option B: Manual Setup

After deploying the Kafka cluster, manually extract the client certificates:

```bash
# Extract knep-client certificate and key
kubectl get secret knep-client -n kafka -o jsonpath='{.data.user\.crt}' | base64 -d > knep-client.crt
kubectl get secret knep-client -n kafka -o jsonpath='{.data.user\.key}' | base64 -d > knep-client.key

# Extract team-a-user certificate and key
kubectl get secret team-a-user -n kafka -o jsonpath='{.data.user\.crt}' | base64 -d > team-a-user.crt
kubectl get secret team-a-user -n kafka -o jsonpath='{.data.user\.key}' | base64 -d > team-a-user.key

# Extract team-b-user certificate and key
kubectl get secret team-b-user -n kafka -o jsonpath='{.data.user\.crt}' | base64 -d > team-b-user.crt
kubectl get secret team-b-user -n kafka -o jsonpath='{.data.user\.key}' | base64 -d > team-b-user.key

# Extract cluster CA certificate
kubectl get secret my-cluster-cluster-ca-cert -n kafka -o jsonpath='{.data.ca\.crt}' | base64 -d > ca.crt
```

## Client Configuration

### Java Client Properties (team-a-user)
```properties
bootstrap.servers=my-cluster-kafka-bootstrap.kafka.svc.cluster.local:9094
security.protocol=SSL
ssl.truststore.location=/path/to/truststore.jks
ssl.truststore.password=changeit
ssl.keystore.location=/path/to/team-a-keystore.jks
ssl.keystore.password=changeit
ssl.key.password=changeit
```

### Creating Java Keystores
```bash
# Create truststore with CA certificate
keytool -import -trustcacerts -alias ca -file ca.crt -keystore truststore.jks -storepass changeit -noprompt

# Create keystore for team-a-user
openssl pkcs12 -export -in team-a-user.crt -inkey team-a-user.key -out team-a-user.p12 -name team-a-user -passout pass:changeit
keytool -importkeystore -deststorepass changeit -destkeypass changeit -destkeystore team-a-keystore.jks -srckeystore team-a-user.p12 -srcstoretype PKCS12 -srcstorepass changeit -alias team-a-user
```

## Testing mTLS Connection

### Using kafka-console-producer
```bash
# Port forward to Kafka mTLS port
kubectl port-forward svc/my-cluster-kafka-bootstrap 9094:9094 -n kafka

# Test with team-a-user (can only access a-* topics)
kafka-console-producer --topic a-test-topic \
  --bootstrap-server localhost:9094 \
  --producer-property security.protocol=SSL \
  --producer-property ssl.truststore.location=truststore.jks \
  --producer-property ssl.truststore.password=changeit \
  --producer-property ssl.keystore.location=team-a-keystore.jks \
  --producer-property ssl.keystore.password=changeit \
  --producer-property ssl.key.password=changeit
```

### Using kafka-console-consumer
```bash
kafka-console-consumer --topic a-test-topic \
  --bootstrap-server localhost:9094 \
  --consumer-property security.protocol=SSL \
  --consumer-property ssl.truststore.location=truststore.jks \
  --consumer-property ssl.truststore.password=changeit \
  --consumer-property ssl.keystore.location=team-a-keystore.jks \
  --consumer-property ssl.keystore.password=changeit \
  --consumer-property ssl.key.password=changeit \
  --consumer-property group.id=team-a-test-group \
  --from-beginning
```

## KNEP Configuration

To configure KNEP to use mTLS when connecting to Kafka, update the KNEP configuration to use the mTLS listener and include the client certificates.

## Security Notes

1. **Certificate Management**: Client certificates are automatically generated by Strimzi
2. **Certificate Rotation**: Strimzi handles automatic certificate rotation
3. **Access Control**: Each user has specific ACLs limiting their access
4. **Network Security**: mTLS provides both authentication and encryption

## Troubleshooting

### Check Certificate Status
```bash
# Check if KafkaUser resources are ready
kubectl get kafkauser -n kafka

# Check certificate secrets
kubectl get secrets -n kafka | grep -E "(knep-client|team-a-user|team-b-user)"

# Verify certificate details
openssl x509 -in team-a-user.crt -text -noout
```

### Common Issues
1. **Certificate not ready**: Wait for KafkaUser to be in Ready state
2. **Wrong permissions**: Check ACL configuration in KafkaUser spec
3. **Network issues**: Ensure port-forward or service connectivity
