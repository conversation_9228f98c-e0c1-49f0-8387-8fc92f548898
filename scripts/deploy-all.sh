#!/bin/bash
set -e

echo "🚀 Deploying KNEP Stack..."

# Create namespaces
echo "📦 Creating namespaces..."
kubectl create namespace kafka --dry-run=client -o yaml | kubectl apply -f -
kubectl create namespace knep --dry-run=client -o yaml | kubectl apply -f -

# Deploy Kafka
echo "☕ Deploying Kafka cluster..."
kubectl apply -f kafka/ -n kafka

# Deploy certificates
echo "🔐 Setting up certificates..."
kubectl apply -f certificates/

# Wait for certificate to be ready (only if using cert-manager)
echo "⏳ Waiting for certificate..."
kubectl wait --for=condition=Ready certificate/knep-certificate -n knep --timeout=300s || echo "⚠️  Certificate not ready yet, continuing..."

# Deploy KNEP
echo "🎯 Deploying KNEP..."
kubectl apply -f knep/ -n knep

# Deploy Kong Gateway
echo "🦍 Deploying Kong Gateway..."
kubectl apply -f kong/ -n knep

echo "✅ Deployment complete!"
echo ""
echo "📋 Check status with:"
echo "  kubectl get pods -n knep"
echo "  kubectl get pods -n kafka"
echo ""
echo "🔍 Check certificate status:"
echo "  kubectl get certificate -n knep"
echo "  kubectl get secret tls-secret -n knep"
