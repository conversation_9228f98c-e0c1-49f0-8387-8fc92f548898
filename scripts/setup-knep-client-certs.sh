#!/bin/bash
set -e

echo "🔐 Setting up KNEP client certificates for mTLS testing..."

# Create certs directory if it doesn't exist
mkdir -p certs

# Extract knep-client certificate and key
echo "📜 Extracting knep-client certificate and key..."
kubectl get secret knep-client -n kafka -o jsonpath='{.data.user\.crt}' | base64 -d > certs/knep-client.crt
kubectl get secret knep-client -n kafka -o jsonpath='{.data.user\.key}' | base64 -d > certs/knep-client.key

# Extract cluster CA certificate
echo "🏛️ Extracting cluster CA certificate..."
kubectl get secret my-cluster-cluster-ca-cert -n kafka -o jsonpath='{.data.ca\.crt}' | base64 -d > certs/ca.crt

# Create Java truststore with CA certificate
echo "🔒 Creating Java truststore..."
keytool -import -trustcacerts -alias ca -file certs/ca.crt -keystore certs/truststore.jks -storepass changeit -noprompt

# Create PKCS12 keystore from client certificate and key
echo "🗝️ Creating PKCS12 keystore..."
openssl pkcs12 -export -in certs/knep-client.crt -inkey certs/knep-client.key -out certs/knep-client.p12 -name knep-client -passout pass:changeit

# Convert PKCS12 to Java keystore
echo "☕ Creating Java keystore..."
keytool -importkeystore -deststorepass changeit -destkeypass changeit -destkeystore certs/knep-client-keystore.jks -srckeystore certs/knep-client.p12 -srcstoretype PKCS12 -srcstorepass changeit -alias knep-client

# Create updated properties file with correct paths
echo "📝 Creating knep-client properties file with correct paths..."
cat > certs/knep-client-local.properties << EOF
# Kafka Client Configuration for KNEP Client (mTLS) - Local Testing
# Generated by setup-knep-client-certs.sh

# Bootstrap servers (use port-forward: kubectl port-forward svc/my-cluster-kafka-bootstrap 9094:9094 -n kafka)
bootstrap.servers=localhost:9094

# Security configuration for mTLS
security.protocol=SSL

# SSL configuration with local certificate paths
ssl.truststore.location=$(pwd)/certs/truststore.jks
ssl.truststore.password=changeit
ssl.keystore.location=$(pwd)/certs/knep-client-keystore.jks
ssl.keystore.password=changeit
ssl.key.password=changeit

# Producer specific settings
acks=all
retries=3
batch.size=16384
linger.ms=1
buffer.memory=33554432

# Consumer specific settings
group.id=knep-client-test-group
auto.offset.reset=earliest
enable.auto.commit=true
auto.commit.interval.ms=1000

# Client ID for identification
client.id=knep-client-test
EOF

echo "✅ Certificate setup complete!"
echo ""
echo "📋 Files created in certs/ directory:"
echo "  - knep-client.crt (client certificate)"
echo "  - knep-client.key (client private key)"
echo "  - ca.crt (cluster CA certificate)"
echo "  - truststore.jks (Java truststore)"
echo "  - knep-client-keystore.jks (Java keystore)"
echo "  - knep-client-local.properties (ready-to-use properties file)"
echo ""
echo "🚀 To test the connection:"
echo "  1. Port forward: kubectl port-forward svc/my-cluster-kafka-bootstrap 9094:9094 -n kafka"
echo "  2. Test topics: kafka-topics --list --bootstrap-server localhost:9094 --command-config certs/knep-client-local.properties"
echo "  3. Create topic: kafka-topics --create --topic test-topic --bootstrap-server localhost:9094 --command-config certs/knep-client-local.properties"
echo ""
echo "⚠️  Note: The knep-client has full access to all topics and operations."
