apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaNodePool
metadata:
  name: dual-role
  labels:
    strimzi.io/cluster: my-cluster
spec:
  replicas: 3
  roles:
    - controller
    - broker
  storage:
    type: jbod
    volumes:
      - id: 0
        type: ephemeral
        kraftMetadata: shared
---
apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: my-cluster
  annotations:
    strimzi.io/node-pools: enabled
    strimzi.io/kraft: enabled
spec:
  kafka:
    version: 3.9.0
    listeners:
      - name: plain
        port: 9092
        type: internal
        tls: false
      - name: tls
        port: 9093
        type: internal
        tls: true
      - name: mtls
        port: 9094
        type: internal
        tls: true
        authentication:
          type: tls
    config:
      offsets.topic.replication.factor: 3
      transaction.state.log.replication.factor: 3
      transaction.state.log.min.isr: 2
      default.replication.factor: 3
      min.insync.replicas: 2
  entityOperator:
    topicOperator: {}
    userOperator: {}
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: knep-client
  labels:
    strimzi.io/cluster: my-cluster
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      # Allow all operations on all topics (for KNEP proxy)
      - resource:
          type: topic
          name: "*"
        operations: ["All"]
      # Allow all operations on consumer groups
      - resource:
          type: group
          name: "*"
        operations: ["All"]
      # Allow cluster operations
      - resource:
          type: cluster
        operations: ["All"]
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: team-a-user
  labels:
    strimzi.io/cluster: my-cluster
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      # Allow operations on topics with 'a-' prefix (team-a topics)
      - resource:
          type: topic
          name: "a-"
          patternType: prefix
        operations: ["Read", "Write", "Create", "Describe"]
      # Allow consumer group operations for team-a
      - resource:
          type: group
          name: "team-a-"
          patternType: prefix
        operations: ["Read"]
---
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: team-b-user
  labels:
    strimzi.io/cluster: my-cluster
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      # Allow operations on topics with 'b-' prefix (team-b topics)
      - resource:
          type: topic
          name: "b-"
          patternType: prefix
        operations: ["Read", "Write", "Create", "Describe"]
      # Allow consumer group operations for team-b
      - resource:
          type: group
          name: "team-b-"
          patternType: prefix
        operations: ["Read"]