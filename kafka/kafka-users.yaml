# KafkaUser for KNEP proxy - requires full access to all topics
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: knep-client
  labels:
    strimzi.io/cluster: my-cluster
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      # Allow all operations on all topics (for KNEP proxy)
      - resource:
          type: topic
          name: "*"
        operations: ["All"]
      # Allow all operations on consumer groups
      - resource:
          type: group
          name: "*"
        operations: ["All"]
      # Allow cluster operations
      - resource:
          type: cluster
        operations: ["All"]
---
# KafkaUser for team-a - restricted to a- prefixed topics
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: team-a-user
  labels:
    strimzi.io/cluster: my-cluster
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      # Allow operations on topics with 'a-' prefix (team-a topics)
      - resource:
          type: topic
          name: "a-"
          patternType: prefix
        operations: ["Read", "Write", "Create", "Describe"]
      # Allow consumer group operations for team-a
      - resource:
          type: group
          name: "team-a-"
          patternType: prefix
        operations: ["Read"]
---
# KafkaUser for team-b - restricted to b- prefixed topics
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: team-b-user
  labels:
    strimzi.io/cluster: my-cluster
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      # Allow operations on topics with 'b-' prefix (team-b topics)
      - resource:
          type: topic
          name: "b-"
          patternType: prefix
        operations: ["Read", "Write", "Create", "Describe"]
      # Allow consumer group operations for team-b
      - resource:
          type: group
          name: "team-b-"
          patternType: prefix
        operations: ["Read"]
