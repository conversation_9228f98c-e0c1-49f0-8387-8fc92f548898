# Additional KafkaUser for administrative access
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: kafka-admin
  labels:
    strimzi.io/cluster: my-cluster
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      # Full admin access to all resources
      - resource:
          type: topic
          name: "*"
        operations: ["All"]
      - resource:
          type: group
          name: "*"
        operations: ["All"]
      - resource:
          type: cluster
        operations: ["All"]
      - resource:
          type: transactionalId
          name: "*"
        operations: ["All"]
---
# KafkaUser for monitoring/metrics access
apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaUser
metadata:
  name: kafka-monitor
  labels:
    strimzi.io/cluster: my-cluster
spec:
  authentication:
    type: tls
  authorization:
    type: simple
    acls:
      # Read-only access for monitoring
      - resource:
          type: topic
          name: "*"
        operations: ["Describe"]
      - resource:
          type: group
          name: "*"
        operations: ["Describe"]
      - resource:
          type: cluster
        operations: ["Describe"]
